using System.Runtime.InteropServices;

namespace AvrStream.Base.Streamer.Devices;

public class AvrVideoCap
{
    public const string NameImageJpeg = "image/jpeg";
    public const string NameVideoXRaw = "video/x-raw";
    public int Width { get; set; }
    public int Height { get; set; }

    public int FpsNumerator { get; set; }
    public int FpsDenominator { get; set; }
    public int Fps { get; set; }

    public bool IsImage { get; set; }
    public string? Format { get; set; } /* null when is image, else: NV12, ... */

    private int NearestFps => FpsNumerator / FpsDenominator;

    public bool IsNative()
    {
        return Fps >= NearestFps - 5;
    }

    public string GetGstCapInput()
    {
        var src = IsImage
            ? $"{NameImageJpeg}, width={Width}, height={Height}, framerate={Fps}/{FpsDenominator},pixel-aspect-ratio=1/1"
            : $"{NameVideoXRaw}, width={Width}, height={Height}, framerate={Fps}/{FpsDenominator}";

        if (IsImage)
        {
            src = RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ? $"{src} ! nvv4l2decoder mjpeg=1 ! nvvidconv" : $"{src} ! jpegdec";
        }

        if (!IsNative()) src = $"{src} ! queue ! videorate drop-only=true max-rate={Fps}";

        return src;
    }
}