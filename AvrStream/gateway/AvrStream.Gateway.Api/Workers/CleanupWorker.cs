using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Databases;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Workers;

public class CleanupWorker : BackgroundService
{
    private readonly ILogger<CleanupWorker> _logger;
    private readonly IConfiguration _configuration;
    private readonly IServiceScopeFactory _factory;

    // Configuration properties
    private CleanupConfiguration _config;

    public CleanupWorker(IServiceScopeFactory factory, ILogger<CleanupWorker> logger, IConfiguration configuration)
    {
        _factory = factory;
        _logger = logger;
        _configuration = configuration;
        LoadConfiguration();
    }

    private void LoadConfiguration()
    {
        _config = new CleanupConfiguration
        {
            IntervalMinutes = _configuration.GetValue<int>("Cleanup:IntervalMinutes", 30),
            KeepAvailableGb = _configuration.GetValue<long?>("Cleanup:KeepAvailableGb"),
            KeepAvailablePercentage = _configuration.GetValue<int?>("Cleanup:KeepAvailablePercentage"),
            DataPath = _configuration.GetValue<string>("DataPath") ??
                      _configuration.GetValue<string>("SystemSetting:RecordPath") ??
                      "/tmp",
            MaxRetries = _configuration.GetValue<int>("Cleanup:MaxRetries", 20),
            RetryDelaySeconds = _configuration.GetValue<int>("Cleanup:RetryDelaySeconds", 15),
            BatchSize = _configuration.GetValue<int>("Cleanup:BatchSize", 10),
            EnableCleanup = _configuration.GetValue<bool>("Cleanup:Enabled", true)
        };

        _logger.LogInformation("Cleanup configuration loaded: " +
            "Interval={IntervalMinutes}min, KeepAvailableGb={KeepAvailableGb}GB, " +
            "KeepAvailablePercentage={KeepAvailablePercentage}%, DataPath={DataPath}, " +
            "MaxRetries={MaxRetries}, BatchSize={BatchSize}, Enabled={EnableCleanup}",
            _config.IntervalMinutes, _config.KeepAvailableGb, _config.KeepAvailablePercentage,
            _config.DataPath, _config.MaxRetries, _config.BatchSize, _config.EnableCleanup);
    }

    private class CleanupConfiguration
    {
        public int IntervalMinutes { get; set; }
        public long? KeepAvailableGb { get; set; }
        public int? KeepAvailablePercentage { get; set; }
        public string DataPath { get; set; }
        public int MaxRetries { get; set; }
        public int RetryDelaySeconds { get; set; }
        public int BatchSize { get; set; }
        public bool EnableCleanup { get; set; }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_config.EnableCleanup)
        {
            _logger.LogInformation("Cleanup worker is disabled by configuration");
            return;
        }

        _logger.LogInformation("Cleanup worker started with interval of {IntervalMinutes} minutes",
            _config.IntervalMinutes);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await PerformCleanupCycle(stoppingToken);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error during cleanup cycle: {Message}", e.Message);
            }

            // Wait for the configured interval before next cleanup cycle
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(_config.IntervalMinutes), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Cleanup worker stopping due to cancellation");
                break;
            }
        }
    }

    private async Task PerformCleanupCycle(CancellationToken stoppingToken)
    {
        using var scope = _factory.CreateScope();
        var resourceService = scope.ServiceProvider.GetService<ResourceService>();

        if (resourceService == null)
        {
            _logger.LogError("ResourceService not available for cleanup");
            return;
        }

        var storageInfo = resourceService.GetStorageInfo(_config.DataPath);
        var shouldCleanup = ShouldPerformCleanup(storageInfo);

        _logger.LogInformation("Storage check: Total={TotalGB}GB, Available={AvailableGB}GB, " +
            "Usage={UsagePercent}%, ShouldCleanup={ShouldCleanup}",
            Math.Round(storageInfo.total / (1024.0 * 1024.0 * 1024.0), 2),
            Math.Round(storageInfo.available / (1024.0 * 1024.0 * 1024.0), 2),
            Math.Round((1.0 - (double)storageInfo.available / storageInfo.total) * 100, 1),
            shouldCleanup);

        if (!shouldCleanup)
        {
            _logger.LogDebug("No cleanup needed at this time");
            return;
        }

        _logger.LogInformation("Starting cleanup process...");
        var cleanupResult = await PerformCleanupWithRetries(resourceService, stoppingToken);

        if (cleanupResult.Success)
        {
            _logger.LogInformation("Cleanup completed successfully. Files cleaned: {FilesDeleted}, " +
                "Space freed: {SpaceFreedMB}MB",
                cleanupResult.FilesDeleted, cleanupResult.SpaceFreedBytes / (1024 * 1024));
        }
        else
        {
            _logger.LogWarning("Cleanup completed with issues. Files cleaned: {FilesDeleted}, " +
                "Errors: {ErrorCount}",
                cleanupResult.FilesDeleted, cleanupResult.ErrorCount);
        }
    }

    private bool ShouldPerformCleanup((long total, long available) storageInfo)
    {
        if (storageInfo.total <= 0 || storageInfo.available < 0)
        {
            _logger.LogWarning("Invalid storage information: total={Total}, available={Available}",
                storageInfo.total, storageInfo.available);
            return false;
        }

        // Check GB threshold
        if (_config.KeepAvailableGb.HasValue && _config.KeepAvailableGb > 0)
        {
            var requiredBytes = _config.KeepAvailableGb.Value * 1024L * 1024L * 1024L;
            if (storageInfo.available < requiredBytes)
            {
                _logger.LogInformation("Cleanup needed: Available {AvailableGB}GB < Required {RequiredGB}GB",
                    Math.Round(storageInfo.available / (1024.0 * 1024.0 * 1024.0), 2),
                    _config.KeepAvailableGb.Value);
                return true;
            }
        }

        // Check percentage threshold
        if (_config.KeepAvailablePercentage.HasValue && _config.KeepAvailablePercentage > 0)
        {
            var availablePercentage = (double)storageInfo.available / storageInfo.total * 100;
            if (availablePercentage < _config.KeepAvailablePercentage.Value)
            {
                _logger.LogInformation("Cleanup needed: Available {AvailablePercent}% < Required {RequiredPercent}%",
                    Math.Round(availablePercentage, 1), _config.KeepAvailablePercentage.Value);
                return true;
            }
        }

        return false;
    }

    private async Task<CleanupResult> PerformCleanupWithRetries(ResourceService resourceService,
        CancellationToken stoppingToken)
    {
        var result = new CleanupResult();
        var retry = 0;

        while (retry < _config.MaxRetries && !stoppingToken.IsCancellationRequested)
        {
            var storageInfo = resourceService.GetStorageInfo(_config.DataPath);
            if (!ShouldPerformCleanup(storageInfo))
            {
                _logger.LogInformation("Cleanup threshold met after {Retries} iterations", retry);
                result.Success = true;
                break;
            }

            var batchResult = await CleanupOldestRecordsBatch(stoppingToken);
            result.FilesDeleted += batchResult.FilesDeleted;
            result.SpaceFreedBytes += batchResult.SpaceFreedBytes;
            result.ErrorCount += batchResult.ErrorCount;

            if (batchResult.FilesDeleted == 0)
            {
                _logger.LogWarning("No more files to cleanup after {Retries} iterations", retry + 1);
                break;
            }

            retry++;

            if (retry < _config.MaxRetries && ShouldPerformCleanup(resourceService.GetStorageInfo(_config.DataPath)))
            {
                try
                {
                    await Task.Delay(TimeSpan.FromSeconds(_config.RetryDelaySeconds), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Cleanup cancelled during retry delay");
                    break;
                }
            }
        }

        if (retry >= _config.MaxRetries)
        {
            _logger.LogWarning("Cleanup stopped after reaching maximum retries ({MaxRetries})", _config.MaxRetries);
        }

        return result;
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("CleanupWorker stopping...");
        return base.StopAsync(cancellationToken);
    }

    private async Task<CleanupResult> CleanupOldestRecordsBatch(CancellationToken stoppingToken)
    {
        var result = new CleanupResult();

        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();

            if (db == null)
            {
                _logger.LogError("Database context not available for cleanup");
                return result;
            }

            // First priority: Clean synchronized files (already uploaded)
            var synchronizedFiles = await db.RecordingFiles
                .AsNoTracking()
                .Where(f => f.IsSynchronized == true)
                .OrderBy(f => f.CreatedAt) // Oldest first
                .Take(_config.BatchSize)
                .ToListAsync(stoppingToken);

            if (synchronizedFiles.Count > 0)
            {
                _logger.LogInformation("Cleaning {Count} synchronized files", synchronizedFiles.Count);
                var batchResult = await CleanupFilesBatch(db, synchronizedFiles, "synchronized", stoppingToken);
                result.FilesDeleted += batchResult.FilesDeleted;
                result.SpaceFreedBytes += batchResult.SpaceFreedBytes;
                result.ErrorCount += batchResult.ErrorCount;
                return result;
            }

            // Second priority: Clean unsynchronized files if no synchronized files available
            var unsynchronizedFiles = await db.RecordingFiles
                .AsNoTracking()
                .Where(f => f.IsSynchronized == false)
                .OrderBy(f => f.CreatedAt) // Oldest first
                .Take(_config.BatchSize)
                .ToListAsync(stoppingToken);

            if (unsynchronizedFiles.Count > 0)
            {
                _logger.LogWarning("No synchronized files available. Cleaning {Count} unsynchronized files",
                    unsynchronizedFiles.Count);
                var batchResult = await CleanupFilesBatch(db, unsynchronizedFiles, "unsynchronized", stoppingToken);
                result.FilesDeleted += batchResult.FilesDeleted;
                result.SpaceFreedBytes += batchResult.SpaceFreedBytes;
                result.ErrorCount += batchResult.ErrorCount;
            }
            else
            {
                _logger.LogInformation("No recording files available for cleanup");
            }

            return result;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error during cleanup batch operation: {Message}", e.Message);
            result.ErrorCount++;
            return result;
        }
    }

    private async Task<CleanupResult> CleanupFilesBatch(GatewayDbContext db,
        List<AvrStream.Gateway.Entities.Models.RecordingFile> files,
        string fileType,
        CancellationToken stoppingToken)
    {
        var result = new CleanupResult();

        foreach (var recordingFile in files)
        {
            if (stoppingToken.IsCancellationRequested)
                break;

            try
            {
                long fileSize = 0;

                // Get file size before deletion
                if (File.Exists(recordingFile.Path))
                {
                    var fileInfo = new FileInfo(recordingFile.Path);
                    fileSize = fileInfo.Length;

                    File.Delete(recordingFile.Path);
                    _logger.LogDebug("Deleted {FileType} file: {FilePath} ({SizeMB}MB)",
                        fileType, recordingFile.Path, Math.Round(fileSize / (1024.0 * 1024.0), 2));
                }
                else
                {
                    _logger.LogWarning("File not found for cleanup: {FilePath}", recordingFile.Path);
                }

                // Remove from database
                db.RecordingFiles.Remove(recordingFile);
                await db.SaveChangesAsync(stoppingToken);

                result.FilesDeleted++;
                result.SpaceFreedBytes += fileSize;
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error cleaning up {FileType} file {FilePath}: {Message}",
                    fileType, recordingFile.Path, e.Message);
                result.ErrorCount++;
            }
        }

        return result;
    }

    private class CleanupResult
    {
        public int FilesDeleted { get; set; }
        public long SpaceFreedBytes { get; set; }
        public int ErrorCount { get; set; }
        public bool Success { get; set; }
    }
}