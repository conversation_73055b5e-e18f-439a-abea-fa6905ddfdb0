using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Databases;
using Microsoft.EntityFrameworkCore;

namespace AvrStream.Gateway.Api.Workers;

public class CleanupWorker : BackgroundService
{
    private readonly ILogger<CleanupWorker> _logger;
    private readonly IConfiguration _configuration;
    private ResourceService _resourceService;
    private readonly IServiceScopeFactory _factory;

    public CleanupWorker(IServiceScopeFactory factory, ILogger<CleanupWorker> logger, IConfiguration configuration)
    {
        _factory = factory;
        _logger = logger;
        _configuration = configuration;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!stoppingToken.IsCancellationRequested)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                using var scope = _factory.CreateScope();
                _resourceService = scope.ServiceProvider.GetService<ResourceService>();
                var retry = 0;
                var availableGb =
                    _configuration.GetValue<long?>("Cleanup:KeepAvailableGb") /* null or a number of Gb */;
                var availablePercentage =
                    _configuration.GetValue<int?>("Cleanup:KeepAvailablePercentage") /* null or 0-100 */;
                var path = _configuration.GetValue<string>("DataPath");
                do
                {
                    if (retry > 20) break;
                    var info = _resourceService.GetStorageInfo(path);
                    _logger.LogWarning(
                        $"Delete oldest record: total={info.total}, available={info.available}, cleanup KeepAvailableGb={availableGb}, cleanup KeepAvailablePercentage={availablePercentage}");
                    // var shouldCleanup = (availableGb > 0 && info.available < availableGb * 1024 * 1024 * 1024) ||
                    //                     (availablePercentage > 0 &&
                    //                      info.available * 1.0 / info.total < availablePercentage / 100.0);
                    // _logger.LogInformation($"Check availableGb: {availableGb > 0 && info.available < availableGb * 1024 * 1024 * 1024}");
                    // _logger.LogInformation($"Check availablePercentage: {(availablePercentage > 0 && info.available * 1.0 / info.total < availablePercentage / 100.0)}");
                    // _logger.LogInformation(
                    //     $"Check for cleanup: total={info.total}, available={info.available}, should cleanup: {shouldCleanup}");
                    // if (!shouldCleanup) break;
                    var result = await CleanupOldestRecord();
                    if (result)
                    {
                        break;
                    }

                    await Task.Delay(TimeSpan.FromSeconds(15), stoppingToken);
                    retry++;
                } while (true);

                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }
    }

    public override Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation($"{nameof(CleanupWorker)} stopped");
        return Task.CompletedTask;
    }

    private async Task<bool> CleanupOldestRecord()
    {
        try
        {
            using var scope = _factory.CreateScope();
            var db = scope.ServiceProvider.GetService<GatewayDbContext>();
            var recordingFiles =
                await db.RecordingFiles.AsNoTracking().Where(f => f.IsSynchronized == true).ToListAsync();
            foreach (var recordingFile in recordingFiles)
            {
                try
                {
                    if (File.Exists(recordingFile.Path))
                    {
                        File.Delete(recordingFile.Path);
                    }

                    db.Remove(recordingFile);
                    await db.SaveChangesAsync();
                }
                catch (Exception e)
                {
                    _logger.LogError(e, null);
                }
            }

            if (recordingFiles.Count == 0)
            {
                recordingFiles = await db.RecordingFiles.AsNoTracking().Where(f => f.IsSynchronized == false)
                    .ToListAsync();
                foreach (var recordingFile in recordingFiles)
                {
                    try
                    {
                        if (File.Exists(recordingFile.Path))
                        {
                            File.Delete(recordingFile.Path);
                        }

                        db.Remove(recordingFile);
                        await db.SaveChangesAsync();
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, null);
                    }
                }
            }

            return true;
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            return false;
        }
    }
}