using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using AvrStream.Base.Models;

namespace AvrStream.Gateway.Api.Services;

public class ResourceService
{
    private readonly ILogger<ResourceService> _logger;

    public ResourceService(ILogger<ResourceService> logger)
    {
        _logger = logger;
    }

    public string GetDiskUsed(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk used")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=used {disk} | grep -v Used\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();
                _logger.LogDebug($"Output: {output}");
                var lines = output.Split("\n");
                _logger.LogDebug($"Output: {lines[0]}");
                return lines[0];
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public string GetDiskTotal(string disk)
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("disk total")
                {
                    FileName = "/bin/bash",
                    Arguments = $"-c \"df -h --output=size {disk} | grep -v Size\"",
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };
                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();
                var lines = output.Split("\n");
                _logger.LogDebug($"Output: {lines[0]}");
                return lines[0];
            }

            return "0 G";
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return null;
        }
    }

    public bool CheckExceededCapacity(double percent, string disk)
    {
        var used = GetDiskUsed(disk);
        var total = GetDiskTotal(disk);
        _logger.LogDebug($"Disk: {disk}");
        _logger.LogDebug($"Used Value Raw: {used}");
        _logger.LogDebug($"Total Value Raw: {total}");
        if (!string.IsNullOrEmpty(used) && !string.IsNullOrEmpty(total))
        {
            var usedUnit = used[^1];
            var totalUnit = total[^1];
            var usedValueString = used.Substring(0, used.Length - 1).Replace(",", ".");
            var totalValueString = total.Substring(0, total.Length - 1).Replace(",", ".");
            double.TryParse(usedValueString, out var usedValue);
            double.TryParse(totalValueString, out var totalValue);
            if (usedUnit != totalUnit)
            {
                usedValue = NumberExtensions.ConvertToGb(usedUnit, usedValue);
                totalValue = NumberExtensions.ConvertToGb(totalUnit, totalValue);
            }

            _logger.LogDebug($"Used Value: {usedValue} GB");
            _logger.LogDebug($"Total Value: {totalValue} GB");
            if (totalValue > 0)
            {
                var percentUsed = usedValue / totalValue * 100;
                _logger.LogDebug($"Percent Used: {Math.Round(percentUsed, 3)}");
                if (percentUsed >= percent)
                {
                    return true;
                }
            }
        }

        return false;
    }

    public double GetMemory()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var info = new ProcessStartInfo("memory")
                {
                    FileName = "/bin/bash",
                    Arguments = "-c \"free -m\"",
                    RedirectStandardOutput = true
                };

                using var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process!.StandardOutput.ReadToEnd();

                var lines = output.Split("\n");
                var memory = lines[1].Split(" ", StringSplitOptions.RemoveEmptyEntries);
                return Math.Round(double.Parse(memory[2]) * 100 / double.Parse(memory[1]), 2);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
        }

        return 0;
    }

    public double GetCpu()
    {
        try
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                // For Jetson Nano, use /proc/stat method to avoid interfering with recording devices
                // This method doesn't hold devices like vmstat or top commands do
                return GetCpuUsageFromProcStat();
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error getting CPU usage: {Message}", e.Message);

            // Fallback to tegrastats for Jetson Nano if available
            try
            {
                if (RuntimeInformation.RuntimeIdentifier.Contains("ubuntu.18.04-arm64") ||
                    RuntimeInformation.RuntimeIdentifier.Contains("linux-arm64"))
                {
                    return GetCpuUsageFromTegrastats();
                }
            }
            catch (Exception fallbackEx)
            {
                _logger.LogError(fallbackEx, "Fallback CPU measurement also failed: {Message}", fallbackEx.Message);
            }

            return -1;
        }

        return 0;
    }

    /// <summary>
    /// Get CPU usage from /proc/stat - safe method that doesn't interfere with recording devices
    /// </summary>
    private double GetCpuUsageFromProcStat()
    {
        try
        {
            // Read /proc/stat twice with a small interval to calculate CPU usage
            var (idle1, total1) = ReadProcStat();

            // Small delay to get meaningful difference
            Thread.Sleep(100);

            var (idle2, total2) = ReadProcStat();

            // Calculate CPU usage percentage
            var idleDiff = idle2 - idle1;
            var totalDiff = total2 - total1;

            if (totalDiff == 0) return 0;

            var cpuUsage = 100.0 * (1.0 - (double)idleDiff / totalDiff);

            _logger.LogDebug("CPU Usage from /proc/stat: {CpuUsage}%", Math.Round(cpuUsage, 2));

            return Math.Max(0, Math.Min(100, cpuUsage)); // Clamp between 0-100
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error reading /proc/stat for CPU usage");
            throw;
        }
    }

    /// <summary>
    /// Read CPU statistics from /proc/stat
    /// </summary>
    private (long idle, long total) ReadProcStat()
    {
        var info = new ProcessStartInfo("cat", "/proc/stat")
        {
            RedirectStandardOutput = true,
            RedirectStandardError = true
        };

        using var process = Process.Start(info);
        if (!process!.WaitForExit(500))
        {
            process.Kill();
            throw new TimeoutException("Reading /proc/stat timed out");
        }

        var output = process.StandardOutput.ReadToEnd();
        var lines = output.Split('\n');

        // First line contains overall CPU stats: cpu user nice system idle iowait irq softirq steal guest guest_nice
        var cpuLine = lines[0];
        if (!cpuLine.StartsWith("cpu "))
        {
            throw new InvalidDataException("Invalid /proc/stat format");
        }

        var values = cpuLine.Split(' ', StringSplitOptions.RemoveEmptyEntries)
            .Skip(1) // Skip "cpu" label
            .Select(long.Parse)
            .ToArray();

        if (values.Length < 4)
        {
            throw new InvalidDataException("Insufficient CPU data in /proc/stat");
        }

        // values[0] = user, values[1] = nice, values[2] = system, values[3] = idle
        // values[4] = iowait, values[5] = irq, values[6] = softirq, values[7] = steal
        var idle = values[3] + (values.Length > 4 ? values[4] : 0); // idle + iowait
        var total = values.Sum();

        return (idle, total);
    }

    /// <summary>
    /// Fallback method using tegrastats for Jetson Nano
    /// </summary>
    private double GetCpuUsageFromTegrastats()
    {
        var info = new ProcessStartInfo("tegrastats")
        {
            RedirectStandardOutput = true,
            RedirectStandardError = true
        };

        using var process = Process.Start(info);
        if (!process!.WaitForExit(2000))
        {
            process.Kill();
            throw new TimeoutException("tegrastats command timed out");
        }

        var output = process.StandardOutput.ReadToEnd();
        _logger.LogDebug("Raw tegrastats output: {Output}", output);

        // Parse tegrastats output: "RAM 2141/3964MB ... CPU [41%@1479,47%@1479,56%@1479,48%@1479] ..."
        var pattern = @"CPU \[(\d+)%@\d+,(\d+)%@\d+,(\d+)%@\d+,(\d+)%@\d+\]";
        var match = Regex.Match(output, pattern);

        if (match.Success)
        {
            var cpu1 = int.Parse(match.Groups[1].Value);
            var cpu2 = int.Parse(match.Groups[2].Value);
            var cpu3 = int.Parse(match.Groups[3].Value);
            var cpu4 = int.Parse(match.Groups[4].Value);

            var totalCpuUsage = (cpu1 + cpu2 + cpu3 + cpu4) / 4.0;

            _logger.LogDebug("CPU Usage from tegrastats: Core1={Cpu1}%, Core2={Cpu2}%, Core3={Cpu3}%, Core4={Cpu4}%, Average={Average}%",
                cpu1, cpu2, cpu3, cpu4, Math.Round(totalCpuUsage, 2));

            return totalCpuUsage;
        }

        throw new InvalidDataException("Could not parse tegrastats output");
    }

    public long GetDiskAvailableKb(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -k --output=avail {disk} | grep -v Avail\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetDiskTotalBytes(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -B1 --output=size {disk} | grep -v 1B-blocks\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            //_logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetDiskAvailableBytes(string disk)
    {
        try
        {
            var info = new ProcessStartInfo("disk available")
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"df -B1 --output=avail {disk} | grep -v Avail\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            //_logger.LogInformation($"Get Available Disk Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public long GetRamTotal()
    {
        try
        {
            var info = new ProcessStartInfo("ram total")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"free -m| grep Mem | awk '{print $2}'\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get Total Ram Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (long.TryParse(valueString, out var value))
                return value;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    // get cpu temperature via (cat /sys/class/thermal/thermal_zone1/temp)
    public int GetCpuTemperature()
    {
        try
        {
            var info = new ProcessStartInfo("cpu temperature")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"cat /sys/class/thermal/thermal_zone1/temp\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get CPU Temperature Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (int.TryParse(valueString, out var value))
                return value / 1000;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public int GetGpuTemperature()
    {
        try
        {
            var info = new ProcessStartInfo("gpu temperature")
            {
                FileName = "/bin/bash",
                Arguments = "-c \"cat /sys/class/thermal/thermal_zone2/temp\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };
            _logger.LogDebug($"Get GPU Temperature Command: {info.FileName} {info.Arguments}");
            using var process = Process.Start(info);
            if (!process!.WaitForExit(1000))
            {
                process.Kill();
            }

            var output = process!.StandardOutput.ReadToEnd();
            var valueString = output.Trim();
            if (int.TryParse(valueString, out int value))
                return value / 1000;
        }
        catch (Exception e)
        {
            _logger.LogError(e.Message);
            return -1;
        }

        return 0;
    }

    public (long total, long available) GetStorageInfo(string dataPath)
    {
        // NOTE: only support removable devices on linux right now^M
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            try
            {
                var info = new ProcessStartInfo("df", "-k")
                {
                    RedirectStandardOutput = true
                };

                var process = Process.Start(info);
                if (!process!.WaitForExit(1000))
                {
                    process.Kill();
                }

                var output = process.StandardOutput.ReadToEnd();
                var blocks = output.Split(new[] { '\n' }, StringSplitOptions.RemoveEmptyEntries)
                    .Skip(1) /* ignore header column */
                    .Select(t =>
                    {
                        var strs = t.Split(new[] { " " },
                            StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);

                        if (strs.Length == 6)
                        {
                            // 1K-blocks in column 1
                            var size = long.Parse(strs[1]) * 1024;
                            var used = long.Parse(strs[2]) * 1024;
                            var available = long.Parse(strs[3]) * 1024;
                            var mount = strs[5];

                            return new
                            {
                                mount,
                                size,
                                used,
                                available
                            };
                        }

                        return null;
                    })
                    .Where(t => t != null)
                    .Select(t => t!)
                    .ToList();

                _logger.LogDebug($"Blocks size info: {JsonSerializer.Serialize(blocks)}");

                var device = blocks.FirstOrDefault(t => dataPath.StartsWith(t.mount)) ??
                             blocks.FirstOrDefault(t => t.mount == "/");

                return (device?.size ?? long.MaxValue, device?.available ?? long.MaxValue);
            }
            catch (Exception e)
            {
                _logger.LogError(e, nameof(GetStorageInfo));
            }
        }

        return (long.MaxValue, long.MaxValue);
    }
}