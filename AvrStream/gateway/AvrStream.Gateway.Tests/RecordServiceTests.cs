using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using AvrStream.Gateway.Api.Services;
using AvrStream.Gateway.Entities.Models;
using AvrStream.Gateway.Entities.Enums;
using AvrStream.Gateway.Streamer;

namespace AvrStream.Gateway.Tests;

[TestClass]
public class RecordServiceTests
{
    private Mock<IConfiguration> _mockConfiguration;
    private Mock<ILoggerFactory> _mockLoggerFactory;
    private Mock<IServiceScopeFactory> _mockServiceScopeFactory;
    private Mock<ILogger<RecordService>> _mockLogger;
    private Mock<MessageService> _mockMessageService;
    private Mock<RecordingFileService> _mockRecordingFileService;
    private Mock<DeviceService> _mockDeviceService;
    private Mock<CameraSettingService> _mockCameraSettingService;
    private RecordService _recordService;

    [TestInitialize]
    public void Setup()
    {
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLoggerFactory = new Mock<ILoggerFactory>();
        _mockServiceScopeFactory = new Mock<IServiceScopeFactory>();
        _mockLogger = new Mock<ILogger<RecordService>>();
        _mockMessageService = new Mock<MessageService>();
        _mockRecordingFileService = new Mock<RecordingFileService>();
        _mockDeviceService = new Mock<DeviceService>();
        _mockCameraSettingService = new Mock<CameraSettingService>();

        _mockLoggerFactory.Setup(x => x.CreateLogger<RecordService>())
            .Returns(_mockLogger.Object);

        _recordService = new RecordService(
            _mockConfiguration.Object,
            _mockLoggerFactory.Object,
            _mockServiceScopeFactory.Object,
            _mockLogger.Object,
            _mockMessageService.Object,
            _mockRecordingFileService.Object,
            _mockDeviceService.Object,
            _mockCameraSettingService.Object
        );
    }

    [TestMethod]
    public void Stop_NonExistentPipeline_ReturnsFalse()
    {
        // Arrange
        var pipelineId = "nonexistent";

        // Act
        var result = _recordService.Stop(pipelineId);

        // Assert
        Assert.IsFalse(result);
        _mockMessageService.Verify(
            x => x.CreateMessage($"Device {pipelineId} not found to stop", MessageType.Error),
            Times.Once
        );
    }

    [TestMethod]
    public void Stop_NotRunningPipeline_ReturnsFalse()
    {
        // Arrange
        var pipelineId = "cam1";
        var mockRecordPipe = new Mock<RecordPipe>();
        var recordPipeManager = new RecordPipeManager
        {
            PipeName = pipelineId,
            IsRunning = false,
            RecordPipe = mockRecordPipe.Object
        };
        _recordService.RecordPipeManagers.Add(recordPipeManager);

        // Act
        var result = _recordService.Stop(pipelineId);

        // Assert
        Assert.IsFalse(result);
        _mockMessageService.Verify(
            x => x.CreateMessage($"Recording device {pipelineId} is not running", MessageType.Alert),
            Times.Once
        );
    }

    [TestMethod]
    public void IsRunning_ExistingRunningPipeline_ReturnsTrue()
    {
        // Arrange
        var pipelineId = "cam1";
        var recordPipeManager = new RecordPipeManager
        {
            PipeName = pipelineId,
            IsRunning = true
        };
        _recordService.RecordPipeManagers.Add(recordPipeManager);

        // Act
        var result = _recordService.IsRunning(pipelineId);

        // Assert
        Assert.IsTrue(result);
    }

    [TestMethod]
    public void IsRunning_ExistingStoppedPipeline_ReturnsFalse()
    {
        // Arrange
        var pipelineId = "cam1";
        var recordPipeManager = new RecordPipeManager
        {
            PipeName = pipelineId,
            IsRunning = false
        };
        _recordService.RecordPipeManagers.Add(recordPipeManager);

        // Act
        var result = _recordService.IsRunning(pipelineId);

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public void IsRunning_NonExistentPipeline_ReturnsFalse()
    {
        // Arrange
        var pipelineId = "nonexistent";

        // Act
        var result = _recordService.IsRunning(pipelineId);

        // Assert
        Assert.IsFalse(result);
    }

    [TestMethod]
    public void GetPipelineStatuses_MultiplePipelines_ReturnsCorrectStatuses()
    {
        // Arrange
        _recordService.RecordPipeManagers.AddRange(new[]
        {
            new RecordPipeManager { PipeName = "cam1", IsRunning = true },
            new RecordPipeManager { PipeName = "cam2", IsRunning = false }
        });

        // Act
        var statuses = _recordService.GetPipelineStatuses();

        // Assert
        Assert.AreEqual(2, statuses.Count);
        Assert.IsTrue(statuses["cam1"]);
        Assert.IsFalse(statuses["cam2"]);
    }

    [TestMethod]
    public void GetPipelineInfo_ExistingPipeline_ReturnsManager()
    {
        // Arrange
        var pipelineId = "cam1";
        var recordPipeManager = new RecordPipeManager
        {
            PipeName = pipelineId,
            IsRunning = true
        };
        _recordService.RecordPipeManagers.Add(recordPipeManager);

        // Act
        var result = _recordService.GetPipelineInfo(pipelineId);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(pipelineId, result.PipeName);
        Assert.IsTrue(result.IsRunning);
    }

    [TestMethod]
    public void GetPipelineInfo_NonExistentPipeline_ReturnsNull()
    {
        // Arrange
        var pipelineId = "nonexistent";

        // Act
        var result = _recordService.GetPipelineInfo(pipelineId);

        // Assert
        Assert.IsNull(result);
    }

    [TestMethod]
    public void ThreadSafety_ConcurrentAccess_NoExceptions()
    {
        // Arrange
        var tasks = new List<Task>();
        var pipelineId = "cam1";
        var recordPipeManager = new RecordPipeManager
        {
            PipeName = pipelineId,
            IsRunning = true
        };
        _recordService.RecordPipeManagers.Add(recordPipeManager);

        // Act - Simulate concurrent access
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() => _recordService.IsRunning(pipelineId)));
            tasks.Add(Task.Run(() => _recordService.GetPipelineStatuses()));
            tasks.Add(Task.Run(() => _recordService.GetPipelineInfo(pipelineId)));
        }

        // Assert - Should complete without exceptions
        Assert.DoesNotThrowAsync(async () => await Task.WhenAll(tasks));
    }

    [TestMethod]
    public void StartingFlags_ThreadSafe_ProperlyManaged()
    {
        // Arrange & Act
        var tasks = new List<Task>();
        
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(Task.Run(() =>
            {
                var isStarting1 = _recordService.IsStarting1;
                var isStarting2 = _recordService.IsStarting2;
                // These should not throw exceptions
            }));
        }

        // Assert
        Assert.DoesNotThrowAsync(async () => await Task.WhenAll(tasks));
    }

    [TestCleanup]
    public void Cleanup()
    {
        _recordService?.Dispose();
    }
}
